"use client"

import React, { createContext, useContext, useState, useEffect, useCallback } from "react"
import { StaffDataService, StaffMember } from "@/lib/staff-data-service"
import { dataCache } from "@/lib/data-cache"
import { revalidateCacheTags } from "@/lib/cache-actions"

// Define the staff context type
interface StaffContextType {
  staff: StaffMember[]
  getStaffById: (id: string) => StaffMember | undefined
  getStaffByLocation: (locationId: string) => StaffMember[]
  getStaffWithHomeService: () => StaffMember[]
  refreshStaff: () => void
  addStaff: (staff: Omit<StaffMember, "id">) => StaffMember
  updateStaff: (staff: StaffMember) => StaffMember | null
  deleteStaff: (staffId: string) => boolean
}

// Create the context with default values
const StaffContext = createContext<StaffContextType>({
  staff: [],
  getStaffById: () => undefined,
  getStaffByLocation: () => [],
  getStaffWithHomeService: () => [],
  refreshStaff: () => {},
  addStaff: () => ({ id: "", name: "", email: "", phone: "", role: "", locations: [], status: "", avatar: "", color: "", homeService: false }),
  updateStaff: () => null,
  deleteStaff: () => false,
})

// Define cache tags for staff data
export const STAFF_CACHE_TAGS = {
  ALL: 'staff:all',
  DIRECTORY: 'staff:directory',
  SCHEDULE: 'staff:schedule',
  CALENDAR: 'staff:calendar',
}

// Create the provider component
export function StaffProvider({ children }: { children: React.ReactNode }) {
  const [staff, setStaff] = useState<StaffMember[]>([])
  const [isInitialized, setIsInitialized] = useState(false)

  // Debug logging for staff state changes
  useEffect(() => {
    console.log("StaffProvider: Staff state changed - count:", staff.length);
    if (staff.length > 0) {
      console.log("StaffProvider: First 3 staff members:", staff.slice(0, 3).map(s => ({ id: s.id.substring(0, 8), name: s.name })));
    }
  }, [staff])

  // Load initial data from API (real database staff)
  useEffect(() => {
    if (isInitialized) return

    const loadStaffFromAPI = async () => {
      try {
        console.log("StaffProvider: Loading staff data from API...")

        // Fetch staff from the API (real database data)
        console.log("StaffProvider: Loading staff data from API...");
        const response = await fetch('/api/staff', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          cache: 'no-cache' // Ensure we get fresh data
        })

        if (!response.ok) {
          throw new Error(`Failed to fetch staff: ${response.status} ${response.statusText}`)
        }

        const data = await response.json()
        const staffData = data.staff || []

        console.log("StaffProvider: Loaded", staffData.length, "staff members from API")

        // Validate staff data structure
        const validStaff = staffData.filter(s => s && s.id && s.name)
        if (validStaff.length !== staffData.length) {
          console.warn("StaffProvider: Some staff records are invalid, filtered from", staffData.length, "to", validStaff.length)
        }

        // Set the staff data
        setStaff(Array.isArray(validStaff) ? validStaff : [])

        if (validStaff.length === 0) {
          console.error("StaffProvider: No valid staff data received from API!")
        }
      } catch (error) {
        console.error("StaffProvider: Error loading staff from API:", error)

        // Fallback to localStorage if API fails
        try {
          console.log("StaffProvider: Attempting localStorage fallback...")
          const rawData = localStorage.getItem("vanity_staff")
          if (rawData) {
            const fallbackData = JSON.parse(rawData)
            console.log("StaffProvider: Using localStorage fallback:", fallbackData.length, "staff members")
            setStaff(Array.isArray(fallbackData) ? fallbackData : [])
          } else {
            console.log("StaffProvider: No localStorage data available")
            setStaff([])
          }
        } catch (fallbackError) {
          console.error("StaffProvider: Fallback also failed:", fallbackError)
          setStaff([])
        }
      }

      setIsInitialized(true)
    }

    loadStaffFromAPI()
  }, [isInitialized])

  // Refresh staff data from API
  const refreshStaff = useCallback(async () => {
    try {
      console.log("StaffProvider: Refreshing staff data from API...")

      // Fetch fresh data from API
      const response = await fetch('/api/staff', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        cache: 'no-cache' // Ensure we get fresh data
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch staff: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      const freshStaff = data.staff || []

      // Validate staff data structure
      const validStaff = freshStaff.filter(s => s && s.id && s.name)
      if (validStaff.length !== freshStaff.length) {
        console.warn("StaffProvider: Some staff records are invalid during refresh, filtered from", freshStaff.length, "to", validStaff.length)
      }

      // Update state
      setStaff(validStaff)

      console.log("StaffProvider: Staff data refreshed:", validStaff.length, "staff members")

      if (validStaff.length === 0) {
        console.error("StaffProvider: No valid staff data received during refresh!")
      }
    } catch (error) {
      console.error("StaffProvider: Error refreshing staff from API:", error)
      // No fallback during refresh - keep existing data
    }
  }, [])

  // Set up event listener for staff updates (only once)
  useEffect(() => {
    // Function to handle staff update events
    const handleStaffUpdated = () => {
      console.log("Staff updated event received, refreshing staff data");
      refreshStaff();
    };

    // Add event listener
    if (typeof window !== 'undefined') {
      window.addEventListener('staff-updated', handleStaffUpdated);
    }

    // Clean up event listener
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('staff-updated', handleStaffUpdated);
      }
    };
  }, []); // Remove refreshStaff dependency to prevent re-adding listeners

  // Save staff to localStorage with debounce (disabled to prevent infinite loops)
  useEffect(() => {
    if (!isInitialized || staff.length === 0) return

    const saveTimeout = setTimeout(() => {
      // Save to localStorage
      StaffDataService.saveStaff(staff)

      // Update the cache
      dataCache.saveToLocalStorage("vanity_staff", staff)

      // Don't dispatch staff-updated event to prevent infinite loops
      // if (typeof window !== 'undefined') {
      //   // Client-side event for components to refresh
      //   window.dispatchEvent(new CustomEvent('staff-updated'))
      // }
    }, 300) // 300ms debounce

    return () => clearTimeout(saveTimeout)
  }, [staff, isInitialized])



  // Get a staff member by ID
  const getStaffById = useCallback((id: string) => {
    return staff.find(s => s.id === id)
  }, [staff])

  // Get staff members by location
  const getStaffByLocation = useCallback((locationId: string) => {
    console.log(`🔍 StaffProvider.getStaffByLocation called with locationId: "${locationId}"`);
    console.log(`🔍 Total staff available: ${staff.length}`);

    let filteredStaff: StaffMember[] = [];

    if (locationId === "all") {
      filteredStaff = staff;
      console.log(`🔍 Location "all" - returning all ${filteredStaff.length} staff members`);
    } else if (locationId === "home") {
      filteredStaff = staff.filter(s => s.homeService === true || s.locations.includes("home"));
      console.log(`🔍 Location "home" - found ${filteredStaff.length} staff with home service`);
      console.log(`🔍 Home service staff:`, filteredStaff.map(s => ({ name: s.name, homeService: s.homeService, locations: s.locations })));
    } else {
      filteredStaff = staff.filter(s => s.locations.includes(locationId));
      console.log(`🔍 Location "${locationId}" - found ${filteredStaff.length} staff assigned to this location`);
      console.log(`🔍 Staff for location "${locationId}":`, filteredStaff.map(s => ({ name: s.name, locations: s.locations })));

      // Debug: Show all staff and their locations for comparison
      console.log(`🔍 All staff locations for debugging:`, staff.map(s => ({ name: s.name, locations: s.locations })));
    }

    return filteredStaff;
  }, [staff])

  // Get staff members with home service
  const getStaffWithHomeService = useCallback(() => {
    return staff.filter(s => s.homeService === true || s.locations.includes("home"))
  }, [staff])

  // Add a new staff member
  const addStaff = useCallback((newStaffData: Omit<StaffMember, "id" | "createdAt" | "updatedAt">) => {
    const newStaff = StaffDataService.addStaff(newStaffData)
    setStaff(prev => [...prev, newStaff])
    return newStaff
  }, [])

  // Update a staff member
  const updateStaff = useCallback((updatedStaff: StaffMember) => {
    const result = StaffDataService.updateStaff(updatedStaff.id, updatedStaff)

    if (result) {
      setStaff(prev =>
        prev.map(s => s.id === updatedStaff.id ? result : s)
      )
    }

    return result
  }, [])

  // Delete a staff member
  const deleteStaff = useCallback((staffId: string) => {
    const result = StaffDataService.deleteStaff(staffId)

    if (result) {
      setStaff(prev => prev.filter(s => s.id !== staffId))
    }

    return result
  }, [])

  return (
    <StaffContext.Provider
      value={{
        staff,
        getStaffById,
        getStaffByLocation,
        getStaffWithHomeService,
        refreshStaff,
        addStaff,
        updateStaff,
        deleteStaff,
      }}
    >
      {children}
    </StaffContext.Provider>
  )
}

// Custom hook to use the staff context
export const useStaff = () => useContext(StaffContext)
