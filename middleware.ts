// Temporarily disabled middleware to fix authentication issues
// Will implement proper session-based filtering in the frontend

// import { withAuth } from "next-auth/middleware"
// import { NextResponse } from "next/server"

// export default withAuth(
//   function middleware(req) {
//     const token = req.nextauth.token

//     // Add user information to headers for API routes
//     if (req.nextUrl.pathname.startsWith('/api/') && token) {
//       const requestHeaders = new Headers(req.headers)
//       requestHeaders.set('x-user-id', token.id as string)
//       requestHeaders.set('x-user-role', token.role as string)
//       requestHeaders.set('x-user-locations', (token.locations as string[]).join(','))

//       return NextResponse.next({
//         request: {
//           headers: requestHeaders,
//         },
//       })
//     }

//     return NextResponse.next()
//   },
//   {
//     callbacks: {
//       authorized: ({ token, req }) => {
//         // Allow access to public routes
//         if (req.nextUrl.pathname.startsWith('/api/auth/')) {
//           return true
//         }

//         // Require authentication for API routes
//         if (req.nextUrl.pathname.startsWith('/api/')) {
//           return !!token
//         }

//         // Allow other routes
//         return true
//       },
//     },
//   }
// )

// export const config = {
//   matcher: [
//     '/api/((?!auth).)*',  // Match all API routes except auth routes
//     '/dashboard/:path*'
//   ]
// }
