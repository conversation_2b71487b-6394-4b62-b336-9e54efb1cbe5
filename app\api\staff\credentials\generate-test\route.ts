import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { generateTemporaryPassword, generateUsername, hashPassword, mapStaffRoleToUserRole } from "@/lib/auth-utils"

/**
 * POST /api/staff/credentials/generate-test
 * 
 * Generate login credentials for one staff member from each location (for testing)
 */
export async function POST() {
  try {
    console.log("🔄 Generating test credentials for one staff member per location...")
    
    // Get all active locations
    const locations = await prisma.location.findMany({
      where: { isActive: true },
      orderBy: { name: 'asc' }
    })

    if (locations.length === 0) {
      return NextResponse.json({ error: "No active locations found" }, { status: 400 })
    }

    const results = []

    for (const location of locations) {
      console.log(`🔄 Processing location: ${location.name}`)
      
      // Find staff members assigned to this location who don't have credentials
      const staffAtLocation = await prisma.staffMember.findMany({
        where: {
          userId: null, // No credentials yet
          status: 'ACTIVE',
          locations: {
            some: {
              locationId: location.id,
              isActive: true
            }
          }
        },
        include: {
          locations: {
            include: {
              location: true
            }
          }
        },
        orderBy: { name: 'asc' }
      })

      if (staffAtLocation.length === 0) {
        console.log(`⚠️ No staff without credentials found for location: ${location.name}`)
        
        // Try to find any staff at this location (even with credentials) for reporting
        const anyStaffAtLocation = await prisma.staffMember.findMany({
          where: {
            locations: {
              some: {
                locationId: location.id,
                isActive: true
              }
            }
          },
          include: { user: true }
        })

        results.push({
          location: {
            id: location.id,
            name: location.name,
            city: location.city
          },
          staff: null,
          status: 'no_available_staff',
          message: `No staff without credentials found. Total staff at location: ${anyStaffAtLocation.length}`,
          existingStaff: anyStaffAtLocation.map(s => ({
            name: s.name,
            hasCredentials: !!s.user
          }))
        })
        continue
      }

      // Select the first staff member for this location
      const selectedStaff = staffAtLocation[0]
      
      try {
        // Generate username and password
        const username = generateUsername(selectedStaff.name, selectedStaff.employeeNumber || undefined)
        const email = `${username}@vanityhub.com`
        const password = generateTemporaryPassword()
        const hashedPassword = hashPassword(password)

        // Create user account
        const user = await prisma.user.create({
          data: {
            email,
            password: hashedPassword,
            role: mapStaffRoleToUserRole(selectedStaff.jobRole || 'staff'),
            isActive: selectedStaff.status === 'ACTIVE'
          }
        })

        // Update staff member with user ID
        await prisma.staffMember.update({
          where: { id: selectedStaff.id },
          data: { userId: user.id }
        })

        results.push({
          location: {
            id: location.id,
            name: location.name,
            city: location.city
          },
          staff: {
            id: selectedStaff.id,
            name: selectedStaff.name,
            employeeNumber: selectedStaff.employeeNumber,
            jobRole: selectedStaff.jobRole
          },
          credentials: {
            email,
            temporaryPassword: password,
            userId: user.id
          },
          status: 'success',
          message: 'Credentials created successfully'
        })

        console.log(`✅ Created credentials for ${selectedStaff.name} at ${location.name}`)
      } catch (error) {
        console.error(`❌ Error creating credentials for staff at ${location.name}:`, error)
        results.push({
          location: {
            id: location.id,
            name: location.name,
            city: location.city
          },
          staff: {
            id: selectedStaff.id,
            name: selectedStaff.name,
            employeeNumber: selectedStaff.employeeNumber,
            jobRole: selectedStaff.jobRole
          },
          status: 'error',
          message: `Failed to create credentials: ${error instanceof Error ? error.message : 'Unknown error'}`
        })
      }
    }

    const successCount = results.filter(r => r.status === 'success').length
    const errorCount = results.filter(r => r.status === 'error').length
    const noStaffCount = results.filter(r => r.status === 'no_available_staff').length

    console.log(`✅ Test credential generation completed: ${successCount} success, ${errorCount} errors, ${noStaffCount} no available staff`)
    
    return NextResponse.json({
      success: true,
      summary: {
        totalLocations: locations.length,
        successfulCredentials: successCount,
        errors: errorCount,
        noAvailableStaff: noStaffCount
      },
      results
    })

  } catch (error) {
    console.error("❌ Error generating test credentials:", error)
    return NextResponse.json({ error: "Failed to generate test credentials" }, { status: 500 })
  }
}
